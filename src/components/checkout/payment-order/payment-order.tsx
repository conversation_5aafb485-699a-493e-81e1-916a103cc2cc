import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@base/button";
import { Badge } from "@commons/badge";
import { ArrowLeft } from "lucide-react";
import { PaymentMethod } from "@/components/checkout";
import { paymentSchema } from "@/pages/checkout/constants";
import type { PaymentFormData } from "@/pages/checkout/constants";
import type { PaymentOrderProps } from "./types";
import { usePayment } from "@/hooks/usePayment";
import { useOrder } from "@/hooks/useOrder";
import { formatPrice, formatOrderDate } from "./helpers";
import type { CreateOrderResponse } from "@/types/order";

export const PaymentOrder: React.FC<PaymentOrderProps> = ({
  order,
  onBackToCreateOrder,
  onPaymentComplete,
}) => {
  const [currentOrder, setCurrentOrder] = useState<CreateOrderResponse>(order);
  const [isLoadingOrder, setIsLoadingOrder] = useState(false);

  const {
    isProcessingPayment,
    paymentError,
    processPaymentFromForm,
    clearPaymentError,
  } = usePayment();

  const { getOrderById } = useOrder();

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      paymentMethod: "",
    },
  });

  // Fetch fresh order data when component mounts
  useEffect(() => {
    const fetchOrderDetails = async () => {
      console.log("PaymentOrder received order:", order);
      console.log("Order ID:", order?.id);

      // Check if order and order.id exist
      if (!order || !order.id) {
        console.error("Order or order ID is missing:", { order });
        return;
      }

      setIsLoadingOrder(true);
      console.log(`Fetching order details for order ID: ${order.id}`);

      try {
        const freshOrder = await getOrderById(order.id);
        if (freshOrder) {
          setCurrentOrder(freshOrder);
          console.log("Fresh order data:", freshOrder);
        }
      } catch (error) {
        console.error("Failed to fetch order details:", error);
      } finally {
        setIsLoadingOrder(false);
      }
    };

    fetchOrderDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onSubmit = async (data: PaymentFormData) => {
    try {
      clearPaymentError();

      const cardProfileId = undefined;
      const token = undefined;

      const paymentResponse = await processPaymentFromForm(
        currentOrder.id,
        data,
        cardProfileId,
        token,
      );

      if (paymentResponse) {
        console.log("Payment processed successfully:", paymentResponse);
        onPaymentComplete();
      }
    } catch (error) {
      console.error("Payment error:", error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={onBackToCreateOrder}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          กลับไปแก้ไข
        </Button>
        <h1 className="text-[32px] font-medium">ชำระเงิน</h1>
      </div>

      <div className="rounded-lg border bg-white p-6 shadow-sm">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-lg font-semibold">
            คำสั่งซื้อ #{currentOrder.id}
          </h2>
          <Badge variant="outline" className="text-xs">
            สร้างเมื่อ {formatOrderDate(currentOrder.created_at)}
          </Badge>
        </div>

        {isLoadingOrder ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-sm text-gray-500">
              กำลังโหลดข้อมูลคำสั่งซื้อ...
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between border-b py-2">
              <span className="font-medium">ยอดรวม</span>
              <span className="text-primary text-lg font-semibold">
                {formatPrice(currentOrder.total_price)}
              </span>
            </div>
          </div>
        )}
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <PaymentMethod control={control} errors={errors} watch={watch} />

        {paymentError && (
          <div className="rounded-lg border border-red-200 bg-red-50 p-4">
            <p className="text-sm text-red-600">{paymentError}</p>
          </div>
        )}

        <Button
          type="submit"
          size="lg"
          className="w-full"
          disabled={isSubmitting || isProcessingPayment}
        >
          {isProcessingPayment
            ? "กำลังประมวลผลการชำระเงิน..."
            : isSubmitting
              ? "กำลังดำเนินการ..."
              : `ชำระเงิน ${formatPrice(currentOrder.total_price)}`}
        </Button>
      </form>
    </div>
  );
};
